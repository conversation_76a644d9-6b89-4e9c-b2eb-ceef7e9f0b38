{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Interchange Framework", "Description": "The Interchange Framework plugin offers a customizable import and export system, with an extensible set of pipelines for handling common file types.", "Category": "Importers", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"], "IsHidden": true, "Modules": [{"Name": "GLTFCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangeNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "InterchangeFactoryNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "InterchangeImport", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "InterchangeMessages", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangeExport", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangePipelines", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "Interchange<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangeCommon", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangeCommonParser", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangeFbxParser", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"], "TargetAllowList": ["Editor", "Program"]}], "Plugins": [{"Name": "USDCore", "Enabled": true}, {"Name": "VariantManager<PERSON><PERSON>nt", "Enabled": true}, {"Name": "VariantManager", "Enabled": true}, {"Name": "InterchangeAssets", "Enabled": true}], "IsExperimentalVersion": false, "SupportedPrograms": ["InterchangeWorker"]}