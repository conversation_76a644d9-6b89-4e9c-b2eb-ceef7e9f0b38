#include "TY_AN_FindTarget.h"

#include "GameplayTagAssetInterface.h"
#include "GameplayTagContainer.h"
#include "FindTargetComponent/TYFindTargetComponent.h"

void UTY_AN_FindTarget::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	AActor* SelfActor = MeshComp->GetOwner();
	if (!IsValid(SelfActor)) return;
	
	UTYFindTargetComponent* TYFindTargetComponent = SelfActor->GetComponentByClass<UTYFindTargetComponent>();
	if (!IsValid(TYFindTargetComponent)) return;
	
	TYFindTargetComponent->FindTarget(FilterRules, SortRule, ExcludeSelf);
	
	// TODO：输入屏蔽逻辑需要修改
	for (AActor* TargetActor : TYFindTargetComponent->FoundTargets)
	{
		FGameplayTagContainer TagContainer;
		CastChecked<IGameplayTagAssetInterface>(TargetActor)->GetOwnedGameplayTags(TagContainer);
		FGameplayTag Tag = FGameplayTag::RequestGameplayTag(FName("Ability.State.UnderControl"));
	
		if (TagContainer.HasTag(Tag))
		{
			TargetActor->GetComponentByClass<UTYFindTargetComponent>()->Controller = SelfActor;
		}
		// TargetActor->GetComponentByClass<ATYFindTargetComponent>()->Controller = SelfActor;
	}
	
	Super::Notify(MeshComp, Animation, EventReference);
}
