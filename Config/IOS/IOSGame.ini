[CommonInputPlatformSettings_IOS CommonInputPlatformSettings]
DefaultInputType=Touch
bSupportsMouseAndKeyboard=False
bSupportsTouch=True
bSupportsGamepad=True
;@TODO: iOS should default to Generic probably: DefaultGamepadName=Generic
DefaultGamepadName=XboxOne
bCanChangeGamepadType=True
;@TODO: iOS+CommonUI:
;+ControllerData=/LyraFramework/UI/Foundation/Input/CommonUI_TouchBrushData.CommonUI_TouchBrushData_C
;+ControllerData=/LyraFramework/UI/Foundation/Input/CommonUI_GenericBrushData.CommonUI_GenericBrushData_C
+ControllerData=/LyraFramework/UI/Foundation/Platform/Input/GamepadXboxOne/CommonInput_Gamepad_XboxOne.CommonInput_Gamepad_XboxOne_C
+ControllerData=/LyraFramework/UI/Foundation/Platform/Input/GamepadPS4/CommonInput_Gamepad_PS4.CommonInput_Gamepad_PS4_C

[/Script/CommonUI.CommonUISettings]
+PlatformTraits=(TagName="Platform.Trait.Input.PrimarlyTouchScreen")
+PlatformTraits=(TagName="Platform.Trait.SingleOnlineUser")

[LyraPlatformSpecificRenderingSettings_IOS LyraPlatformSpecificRenderingSettings]
FramePacingMode=MobileStyle
bSupportsGranularVideoQualitySettings=false
bSupportsAutomaticVideoQualityBenchmark=false
