// Fill out your copyright notice in the Description page of Project Settings.


#include "TYAnimationBlueprintLibrary.h"

void UTYAnimationBlueprintLibrary::RenameMontageSlotName(UAnimMontage* Montage, int32 SlotIndex, FString NewSlotName)
{
	if (!IsValid(Montage))
		return;
	
	if(Montage->SlotAnimTracks.IsValidIndex(SlotIndex))
	{
		FName NewName(*NewSlotName);
		if(Montage->SlotAnimTracks[SlotIndex].SlotName != NewName)
		{
			// const FScopedTransaction Transaction( LOCTEXT("RenameSlot", "Rename Slot") );
			// WeakModel.Pin()->GetEditableSkeleton()->RegisterSlotNode(NewName);

			Montage->Modify();

			Montage->SlotAnimTracks[SlotIndex].SlotName = NewName;
			Montage->PostEditChange();
			Montage->MarkPackageDirty();
		}
	}
}
