// Fill out your copyright notice in the Description page of Project Settings.


#include "Input/STInputComponent.h"

#include "GameFramework/Character.h"

// Sets default values for this component's properties
USTInputComponent::USTInputComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;
	// ...
}

FVector USTInputComponent::GetInputWorldVector()
{
	if (InputVector.IsNearlyZero())
	{
		return LastInputVector;
	}
	return InputVector;
}

void USTInputComponent::GetInputRelativeDegrees(float& Degrees, FVector& RelativeVector)
{
	auto InputWorldVector = this->GetInputWorldVector();

	if (InputWorldVector.IsNearlyZero())
	{
		Degrees = 0;
		RelativeVector = FVector::ZeroVector;
		return;
	}

	auto Owner = Cast<ACharacter>(this->GetOwner());
	if (Owner == nullptr)
	{
		return;
	}
	FTransform OwnerTransform = Owner->GetTransform();
	if (Owner->GetMesh())
	{
		OwnerTransform = Owner->GetMesh()->GetComponentTransform();
	}
	//转换到角色坐标系下的矢量方向
	RelativeVector = OwnerTransform.InverseTransformVector(InputWorldVector);
	//向量与局部坐标系X轴的夹角，[0,360]，逆时针
	Degrees = FMath::RadiansToDegrees(RelativeVector.ToOrientationQuat().GetAngle());

	return;
}

void USTInputComponent::SetInputWorldVector(FVector InInputVector)
{
	this->InputVector = InInputVector;
}

// Called when the game starts
void USTInputComponent::BeginPlay()
{
	Super::BeginPlay();

	// ...
}


// Called every frame
void USTInputComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	this->LastInputVector = this->InputVector;
	this->InputVector = FVector::Zero();
}

