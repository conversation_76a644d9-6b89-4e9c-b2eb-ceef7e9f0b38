// Fill out your copyright notice in the Description page of Project Settings.

#include "TroopFormation/STTroopFormationActorComponent.h"
#include "TroopFormation/DA/DA_FormationTroop.h"
#include "TroopFormation/STTroopFormationSubsystem.h"
#include "Components/SplineComponent.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"

DEFINE_LOG_CATEGORY_STATIC(LogSTTroopFormationActorComponent, Log, All);

USTTroopFormationActorComponent::USTTroopFormationActorComponent()
{
	// Set this component to be ticked every frame
	PrimaryComponentTick.bCanEverTick = false;

	// 初始化默认值
	ConfigFormationId = 1;
	CanMove = false;
	ActorSpace = 100.0f;
	SplineAngleOffset = 0.0f;
	FormationId = -1;
}

void USTTroopFormationActorComponent::RegisterSubsystem(const TArray<FFormationPoint> &InFormationPoints)
{
	// 获取子系统
	UWorld *World = GetWorld();
	if (!World)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("RegisterSubsystem: World is null"));
		return;
	}

	USTTroopFormationSubsystem *TroopSubsystem = World->GetSubsystem<USTTroopFormationSubsystem>();
	if (!TroopSubsystem)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("RegisterSubsystem: Failed to get STTroopFormationSubsystem"));
		return;
	}

	// 检查必要的配置
	if (!FormationTroop)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("RegisterSubsystem: FormationTroop is null"));
		return;
	}

	if (InFormationPoints.Num() == 0)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("RegisterSubsystem: No formation points generated"));
		return;
	}

	// 1. 从子系统获取一个预分配ID的参数对象
	FormationParameter = TroopSubsystem->RegisterFormationParameter();

	// 2. 根据已有信息填写参数
	FormationParameter.FormationTroopDA = FormationTroop;
	FormationParameter.FormationId = ConfigFormationId;
	FormationParameter.CanMove = CanMove;
	FormationParameter.TotalCount = InFormationPoints.Num();

	// 使用样条线点的中心作为编队中心点
	if (InFormationPoints.Num() > 0)
	{
		FVector CenterPoint = FVector::ZeroVector;
		for (const FFormationPoint &Point : InFormationPoints)
		{
			CenterPoint += Point.Transform.GetLocation();
		}
		FormationParameter.FormationPivot = CenterPoint / InFormationPoints.Num();
	}

	// 转换样条线点为世界变换
	FormationParameter.Transforms.Reserve(FormationParameter.TotalCount);

	// 获取TransformOffset
	FTransform TransformOffset = FTransform::Identity;
	if (FormationTroop->VisualizationDataAsset && FormationTroop->VisualizationDataAsset->bUseTransformOffset)
	{
		TransformOffset = FormationTroop->VisualizationDataAsset->TransformOffset;
	}

	for (int32 i = 0; i < InFormationPoints.Num(); i++)
	{
		const FFormationPoint &Point = InFormationPoints[i];
		const FVector SpawnPoint = Point.Transform.GetLocation();
		const FQuat SpawnRotation = Point.Transform.GetRotation();

		// 应用位置偏移
		FVector OffsetPosition = SpawnPoint + TransformOffset.GetLocation();
		FVector Scale = TransformOffset.GetScale3D();

		// 组合旋转：预计算的样条线朝向 + 偏移旋转
		FQuat CombinedRotation = SpawnRotation * TransformOffset.GetRotation();

		FTransform WorldTransform(CombinedRotation, OffsetPosition, Scale);
		FormationParameter.Transforms.Add(WorldTransform);
	}

	// 3. 调用子系统的创建接口
	int32 ParameterId = TroopSubsystem->CreateTroopFormationWithMass(FormationParameter);

	if (ParameterId != -1)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Log, TEXT("RegisterSubsystem: Successfully created formation with ParameterId %d, FormationId %d, %d entities, Pivot: %s, AngleOffset: %f"),
			   ParameterId, ConfigFormationId, FormationParameter.TotalCount, *FormationParameter.FormationPivot.ToString(), SplineAngleOffset);
		FormationId = ParameterId; // 保存返回的ParameterId
	}
	else
	{
		UE_LOG(LogSTTroopFormationActorComponent, Error, TEXT("RegisterSubsystem: Failed to create formation"));
	}
}

TArray<FFormationPoint> USTTroopFormationActorComponent::GeneratePointsFromSpline(USplineComponent *InSplineComponent)
{
	if (!InSplineComponent)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("GeneratePointsFromSpline: SplineComponent is null"));
		return TArray<FFormationPoint>();
	}
	if (!this->FormationTroop)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Error, TEXT("FormationTroop is null, cannot generate points from spline"));
		return TArray<FFormationPoint>();
	}

	TArray<FFormationPoint> GeneratedPoints;

	// 获取样条线长度
	float SplineLength = InSplineComponent->GetSplineLength();
	if (SplineLength <= 0.0f)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("GeneratePointsFromSpline: Spline length is zero"));
		return GeneratedPoints;
	}

	// 计算实际使用的间隔距离
	float EffectiveActorSpace = ActorSpace;
	EffectiveActorSpace = FormationTroop->TroopRadius * 2 + ActorSpace;

	// 计算需要生成的点数量
	int32 PointCount = FMath::FloorToInt(SplineLength / EffectiveActorSpace);
	if (PointCount <= 0)
	{
		UE_LOG(LogSTTroopFormationActorComponent, Warning, TEXT("GeneratePointsFromSpline: No points to generate (SplineLength: %f, EffectiveActorSpace: %f)"), SplineLength, EffectiveActorSpace);
		return GeneratedPoints;
	}

	// 沿样条线按间距生成点和旋转
	for (int32 i = 0; i <= PointCount; i++)
	{
		float Distance = i * EffectiveActorSpace;

		// 确保不超过样条线长度
		if (Distance > SplineLength)
		{
			Distance = SplineLength;
		}

		// 获取世界坐标位置
		FVector WorldPosition = InSplineComponent->GetWorldLocationAtDistanceAlongSpline(Distance);

		// 先添加一个默认点（Location 已知，Rotation 先设为Identity），保证CalculateRotationAtPoint可以使用已生成的点进行计算
		GeneratedPoints.Add(FFormationPoint(WorldPosition, FQuat::Identity));

		// 计算该点的旋转
		FQuat PointRotation = CalculateRotationAtPoint(InSplineComponent, Distance, i, GeneratedPoints);
		GeneratedPoints[i].Transform.SetRotation(PointRotation);

		// 如果已经到达样条线末端，停止生成
		if (Distance >= SplineLength)
		{
			break;
		}
	}

	UE_LOG(LogSTTroopFormationActorComponent, Log, TEXT("GeneratePointsFromSpline: Generated %d formation points along spline (Length: %f, EffectiveSpace: %f, TroopRadius: %f)"),
		   GeneratedPoints.Num(), SplineLength, EffectiveActorSpace, FormationTroop ? FormationTroop->TroopRadius : 0.0f);

	return GeneratedPoints;
}

FQuat USTTroopFormationActorComponent::CalculateRotationAtPoint(USplineComponent *InSplineComponent, float Distance, int32 PointIndex, const TArray<FFormationPoint> &Points)
{
	// 计算样条线在该点的切线方向
	FVector SplineTangent = InSplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
	SplineTangent = SplineTangent.GetSafeNormal();

	// 如果无法从样条线获取切线，使用相邻点计算
	if (SplineTangent.IsNearlyZero())
	{
		if (PointIndex < Points.Num() - 1 && Points.Num() > PointIndex + 1)
		{
			SplineTangent = (Points[PointIndex + 1].Transform.GetLocation() - Points[PointIndex].Transform.GetLocation()).GetSafeNormal();
		}
		else if (PointIndex > 0)
		{
			SplineTangent = (Points[PointIndex].Transform.GetLocation() - Points[PointIndex - 1].Transform.GetLocation()).GetSafeNormal();
		}
		else
		{
			// 默认朝向
			SplineTangent = FVector::ForwardVector;
		}
	}

	// 根据角度偏移计算最终朝向
	// 将角度转换为弧度
	float AngleRadians = FMath::DegreesToRadians(SplineAngleOffset);

	// 计算垂直于切线的向量（右侧方向）
	FVector RightVector = FVector::CrossProduct(SplineTangent, FVector::UpVector).GetSafeNormal();

	// 根据角度偏移计算最终朝向
	FVector FinalDirection = FMath::Cos(AngleRadians) * SplineTangent + FMath::Sin(AngleRadians) * RightVector;
	FinalDirection = FinalDirection.GetSafeNormal();

	// 创建旋转四元数
	FQuat SplineRotation = FQuat::FindBetweenNormals(FVector::ForwardVector, FinalDirection);

	return SplineRotation;
}
