#include "TroopFormation/STAsyncFormationMoveNode.h"
#include "AIController.h"
#include "GameFramework/Actor.h"
#include "TimerManager.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Navigation/PathFollowingComponent.h"


USTAsyncFormationMoveNode::USTAsyncFormationMoveNode(const FObjectInitializer& ObjectInitializer): Super(ObjectInitializer)
{
}

USTAsyncFormationMoveNode* USTAsyncFormationMoveNode::MoveFormationAsync(UObject* WorldContextObject, const FFormationMoveBatch& MoveBatch, float AcceptanceRadius)
{
    USTAsyncFormationMoveNode* Node = nullptr;
	if (UWorld* World = GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull))
	{
	    Node = NewObject<USTAsyncFormationMoveNode>();
	    Node->InternalBatch = MoveBatch;
	    Node->Radius = AcceptanceRadius;
	    Node->CachedWorld = GEngine->GetWorldFromContextObjectChecked(WorldContextObject);   
	}

    return Node;
}

void USTAsyncFormationMoveNode::Activate()
{
    if (!CachedWorld.IsValid())
    {
        SetReadyToDestroy();
        return;
    }

    // MoveTo for each command
    for (FFormationMoveCommand& Cmd : InternalBatch.Commands)
    {
        if (!Cmd.TargetActor.IsValid())
        {
            continue;
        }
        AAIController* AICon = Cast<AAIController>(Cmd.TargetActor->GetInstigatorController());
        if (!AICon)
        {
            continue;
        }

        // 仅支持 ACharacter，若目标不是 Character 则报错并跳过
        ACharacter* Character = Cast<ACharacter>(Cmd.TargetActor.Get());
        if (!Character)
        {
            UE_LOG(LogTemp, Error, TEXT("STAsyncFormationMoveNode: TargetActor %s is not a Character, skip."), *GetNameSafe(Cmd.TargetActor.Get()));
            continue;
        }
        if (Character->GetCharacterMovement())
        {
            Character->GetCharacterMovement()->MaxWalkSpeed = Cmd.MoveSpeed;
        }

        FAIMoveRequest Req;
        FVector GoalLocation;
        if (Cmd.MoveType == EFormationMoveType::ByOffset)
        {
            GoalLocation = Cmd.TargetActor->GetActorLocation() + Cmd.Offset;
        }
        else
        {
            GoalLocation = Cmd.TargetPoint.Transform.GetLocation();
        }
        Req.SetGoalLocation(GoalLocation);
        Req.SetAcceptanceRadius(Radius);
        Cmd.RequestId = AICon->MoveTo(Req).MoveId;
    }

    CachedWorld->GetTimerManager().SetTimer(TickHandle, this, &USTAsyncFormationMoveNode::TickCheck, 0.1f, true);
}

void USTAsyncFormationMoveNode::TickCheck()
{
    bool bAllDone = true;
    for (const FFormationMoveCommand& Cmd : InternalBatch.Commands)
    {
        if (!Cmd.TargetActor.IsValid())
        {
            continue;
        }
        AAIController* AICon = Cast<AAIController>(Cmd.TargetActor->GetInstigatorController());
        if (!AICon)
        {
            continue;
        }
        if (!AICon->GetPathFollowingComponent()->DidMoveReachGoal())
        {
            bAllDone = false;
            break;
        }
    }

    if (bAllDone)
    {
        if (CachedWorld.IsValid())
        {
            CachedWorld->GetTimerManager().ClearTimer(TickHandle);
        }

        // 移动完成后，根据formation的旋转修改actor的旋转
        for (const FFormationMoveCommand& Cmd : InternalBatch.Commands)
        {
            if (!Cmd.TargetActor.IsValid())
            {
                continue;
            }

            // 设置Actor的旋转为目标点的旋转
            if (Cmd.MoveType == EFormationMoveType::ToTargetPoint)
            {
                FQuat TargetRotation = Cmd.TargetPoint.Transform.GetRotation();
                Cmd.TargetActor->SetActorRotation(TargetRotation);
            }
        }

        Completed.Broadcast();
        SetReadyToDestroy();
    }
}
