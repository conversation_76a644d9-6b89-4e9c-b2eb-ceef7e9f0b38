// Copyright Epic Games, Inc. All Rights Reserved.

#include "HeterogeneousVolumesRayMarchingTypes.ush"
#include "HeterogeneousVolumesTracingUtils.ush"
#include "HeterogeneousVolumesTransmittanceVolumeUtils.ush"

#include "../ForwardShadowingCommon.ush"
#include "../VolumeLightingCommon.ush"
#include "../LightData.ush"
#include "../DeferredLightingCommon.ush"

#include "../SHCommon.ush"
#define FrontLayerTranslucencyReflectionsStruct LumenGIVolumeStruct
#define RadianceCacheInterpolation LumenGIVolumeStruct
#include "../Lumen/LumenTranslucencyVolumeShared.ush"

#ifndef CUSTOM_TRANSMITTANCE_FUNCTION
#define CUSTOM_TRANSMITTANCE_FUNCTION 0
#endif // CUSTOM_TRANSMITTANCE_FUNCTION

#ifndef HARDWARE_RAY_TRACING
#define HARDWARE_RAY_TRACING 0
#endif // HARDWARE_RAY_TRACING

#ifndef HARD_SURFACE_SHADOWING
#define HARD_SURFACE_SHADOWING 1
#endif // HARD_SURFACE_SHADOWING

#ifndef DIM_USE_ADAPTIVE_VOLUMETRIC_SHADOW_MAP
#define DIM_USE_ADAPTIVE_VOLUMETRIC_SHADOW_MAP 0
#endif // DIM_USE_ADAPTIVE_VOLUMETRIC_SHADOW_MAP

#define APPLY_FOG_INSCATTERING_OFF 0
#define APPLY_FOG_INSCATTERING_REFERENCE 1
#define APPLY_FOG_INSCATTERING_STOCHASTIC 2

#ifndef APPLY_FOG_INSCATTERING
#define APPLY_FOG_INSCATTERING APPLY_FOG_INSCATTERING_OFF
#endif // APPLY_FOG_INSCATTERING

#ifndef IS_OFFLINE_RENDER
#define IS_OFFLINE_RENDER 0
#endif // IS_OFFLINE_RENDER

#if DIM_USE_ADAPTIVE_VOLUMETRIC_SHADOW_MAP
#include "HeterogeneousVolumesAdaptiveVolumetricShadowMapSampling.ush"
#include "HeterogeneousVolumesAdaptiveVolumetricShadowMapSampler.ush"
#endif // DIM_USE_ADAPTIVE_VOLUMETRIC_SHADOW_MAP

#if APPLY_FOG_INSCATTERING
#include "../HeightFogCommon.ush"
#endif // APPLY_FOG_INSCATTERING

float CalcShadowBias()
{
	float WorldShadowBias = 0.0f;
	float3 LightingCacheResolutionAsFloat3 = GetLightingCacheResolution();
	if (all(LightingCacheResolutionAsFloat3 > 0))
	{
		float3 VolumeBounds = 2.0f * GetLocalBoundsExtent();
		float3 VoxelSize = VolumeBounds * rcp(LightingCacheResolutionAsFloat3);
		float3 WorldVoxelSize = mul(float4(VoxelSize, 0), GetLocalToWorld()).xyz;
		float VoxelDiagonal = length(WorldVoxelSize);
		WorldShadowBias = VoxelDiagonal * GetLightingCacheVoxelBias();
	}
	return WorldShadowBias;
}

float ComputeHardSurfaceShadowFactor(
	float3 TranslatedWorldPosition,
	FDeferredLightData LightData,
	uint LightType
)
{
	float HardSurfaceShadowFactor = 1.0;

#if HARD_SURFACE_SHADOWING
	// Evaluate hard-surface shadow term
	if (LightType == LIGHT_TYPE_DIRECTIONAL)
	{
		float SceneDepth = dot(TranslatedWorldPosition - PrimaryView.TranslatedWorldCameraOrigin, View.ViewForward);
		bool bShadowingFromValidUVArea = false;
		float ShadowFactor = ComputeDirectionalLightDynamicShadowing(TranslatedWorldPosition, SceneDepth, bShadowingFromValidUVArea);

		if (bShadowingFromValidUVArea)
		{
			HardSurfaceShadowFactor *= ShadowFactor;
		}
	}
	else // Local lights
	{
		bool bShadowingFromValidUVArea = false;
		float ShadowFactor = ComputeVolumeShadowing(TranslatedWorldPosition, LightData.bRadialLight && !LightData.bSpotLight, LightData.bSpotLight, bShadowingFromValidUVArea);

		if (bShadowingFromValidUVArea)
		{
			HardSurfaceShadowFactor *= ShadowFactor;
		}
	}

#if VIRTUAL_SHADOW_MAP
	if (VirtualShadowMapId != INDEX_NONE)
	{
		FVirtualShadowMapSampleResult VirtualShadowMapSample = SampleVirtualShadowMapTranslatedWorld(VirtualShadowMapId, TranslatedWorldPosition);
		HardSurfaceShadowFactor *= VirtualShadowMapSample.ShadowFactor;
	}
#endif // VIRTUALSHADOW_MAP

#endif // HARD_SURFACE_SHADOWING

	return HardSurfaceShadowFactor;
}

void RayMarchTransmittance(
	inout FRayMarchingContext RayMarchingContext,
	uint StepCount,
	inout float3 Transmittance
)
{
	for (uint StepIndex = 0; StepIndex < StepCount; ++StepIndex)
	{
		float LocalHitT = RayMarchingContext.LocalRayTMin + RayMarchingContext.StepSize * (RayMarchingContext.Jitter + StepIndex);
		float3 LocalPosition = RayMarchingContext.LocalRayOrigin + RayMarchingContext.LocalRayDirection * LocalHitT;
		float3 WorldPosition = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection * LocalHitT * RayMarchingContext.LocalToWorldScale;
#if USE_ANALYTIC_DERIVATIVES
		float3 WorldPosition_DDX = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDX * LocalHitT * RayMarchingContext.LocalToWorldScale;
		float3 WorldPosition_DDY = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDY * LocalHitT * RayMarchingContext.LocalToWorldScale;
		WorldPosition_DDX -= WorldPosition;
		WorldPosition_DDY -= WorldPosition;
#else
		float3 WorldPosition_DDX = 0.0;
		float3 WorldPosition_DDY = 0.0;
#endif // USE_ANALYTIC_DERIVATIVES

		FVolumeSampleContext SampleContext = CreateVolumeSampleContext(LocalPosition, WorldPosition, WorldPosition_DDX, WorldPosition_DDY, RayMarchingContext.MipLevel);
		float3 Extinction = SampleExtinction(SampleContext);
		Transmittance *= exp(-Extinction * RayMarchingContext.StepSize);

		float Epsilon = 1.0e-7;
		if (all(Transmittance < Epsilon))
		{
			Transmittance = 0.0;
			break;
		}
	}
}

float3 ComputeTransmittance(
	inout FRayMarchingContext RayMarchingContext
)
{
#if CUSTOM_TRANSMITTANCE_FUNCTION
	float WorldRayTMin = RayMarchingContext.LocalRayTMin * RayMarchingContext.LocalToWorldScale;
	float WorldRayTMax = RayMarchingContext.LocalRayTMax * RayMarchingContext.LocalToWorldScale;
	return TransmittanceFunction(RayMarchingContext.WorldRayOrigin, RayMarchingContext.WorldRayDirection, WorldRayTMin, WorldRayTMax);
#elif HARDWARE_RAY_TRACING
	//  TODO: Incorporpate ray jitter
	return ComputeTransmittanceHardwareRayTracing(RayMarchingContext.WorldRayOrigin, RayMarchingContext.WorldRayDirection, RayMarchingContext.LocalRayTMin, RayMarchingContext.LocalRayTMax);
#else // HARDWARE_RAY_TRACING

	float3 Transmittance = 1.0;

	float3 LocalBoundsMin = GetLocalBoundsOrigin() - GetLocalBoundsExtent();
	float3 LocalBoundsMax = GetLocalBoundsOrigin() + GetLocalBoundsExtent();
	float2 HitT = IntersectAABB(RayMarchingContext.LocalRayOrigin, RayMarchingContext.LocalRayDirection, RayMarchingContext.LocalRayTMin, RayMarchingContext.LocalRayTMax,
		LocalBoundsMin, LocalBoundsMax);

	float HitSpan = HitT.y - HitT.x;
	if (HitSpan > 0.0)
	{
		RayMarchingContext.LocalRayTMin = HitT.x;
		RayMarchingContext.LocalRayTMax = HitT.y;
		uint StepCount = CalcStepCount(RayMarchingContext);

		RayMarchTransmittance(RayMarchingContext, StepCount, Transmittance);
	}

	return Transmittance;
#endif // HARDWARE_RAY_TRACING
}

float3 ComputeTransmittance(
	float3 WorldRayOrigin,
	float3 ToLight,
	uint MaxStepCount
)
{
	float3 Transmittance = 1.0;

#if DIM_USE_TRANSMITTANCE_VOLUME
	float3 LocalRayOrigin = mul(float4(WorldRayOrigin, 1.0), GetWorldToLocal()).xyz;
	float3 LocalBoundsMin = GetLocalBoundsOrigin() - GetLocalBoundsExtent();
	float3 TransmittanceUVW = saturate((LocalRayOrigin - LocalBoundsMin) / (2.0 * GetLocalBoundsExtent()));
	float MipLevel = 0;

	Transmittance = SampleLightingCache(TransmittanceUVW, MipLevel);

#else
	#if DIM_USE_ADAPTIVE_VOLUMETRIC_SHADOW_MAP
		if (!AVSM.bIsEmpty)
		{
			float3 TranslatedWorldPosition = DFFastAddDemote(WorldRayOrigin, PrimaryView.PreViewTranslation);
			float3 LightTranslatedWorldPosition = TranslatedWorldPosition + ToLight;
			Transmittance = AVSM_SampleTransmittance(TranslatedWorldPosition, LightTranslatedWorldPosition);
		}
		else
	#endif
		{
			float3 WorldRayEnd = WorldRayOrigin + ToLight;
			float3 WorldRayDirection = normalize(ToLight);

			float3 LocalRayOrigin = mul(float4(WorldRayOrigin, 1.0), GetWorldToLocal()).xyz;
			float3 LocalRayEnd = mul(float4(WorldRayEnd, 1.0), GetWorldToLocal()).xyz;
			float3 LocalRayDirection = LocalRayEnd - LocalRayOrigin;
			float LocalRayTMin = 0.0;
			float LocalRayTMax = length(LocalRayDirection);
			LocalRayDirection /= LocalRayTMax;

			float ShadowBias = 0.5;
			float ShadowStepSize = CalcShadowStepSize(LocalRayDirection);
			int bApplyEmissionAndTransmittance = 0;
			int bApplyDirectLighting = 0;
			int bApplyShadowTransmittance = 0;

			FRayMarchingContext ShadowRayMarchingContext = CreateRayMarchingContext(
				LocalRayOrigin,
				LocalRayDirection,
				LocalRayTMin,
				LocalRayTMax,
				WorldRayOrigin,
				WorldRayDirection,
				ShadowBias,
				ShadowStepSize,
				MaxStepCount,
				bApplyEmissionAndTransmittance,
				bApplyDirectLighting,
				bApplyShadowTransmittance
				//RayMarchingContext.MaxShadowTraceDistance
			);

			Transmittance = ComputeTransmittance(ShadowRayMarchingContext);
		}
#endif
	return Transmittance;
}

float3 ComputeInscattering(
	float3 WorldPosition,
	FDeferredLightData LightData,
	uint LightType,
	uint MaxStepCount,
	float WorldShadowBias,
	bool bApplyShadowTransmittance
)
{
	float3 Inscattering = 0.0;

#if DIM_USE_INSCATTERING_VOLUME
	float3 LocalShadowRayOrigin = mul(float4(WorldPosition, 1.0), GetWorldToLocal()).xyz;
	float3 LocalBoundsMin = GetLocalBoundsOrigin() - GetLocalBoundsExtent();
	float3 TransmittanceUVW = saturate((LocalShadowRayOrigin - LocalBoundsMin) / (2.0 * GetLocalBoundsExtent()));
	float MipLevel = 0;

	Inscattering = SampleLightingCache(TransmittanceUVW, MipLevel) * View.OneOverPreExposure;

#else // DIM_USE_INSCATTERING_VOLUME
	float3 L = LightData.Direction;
	//float3 ToLight = L * RayMarchingContext.MaxShadowTraceDistance;
	float3 ToLight = L * 10000;

	float LightAttenuation = 1.0;
	float3 TranslatedWorldPosition = DFFastToTranslatedWorld(WorldPosition, PrimaryView.PreViewTranslation);
	if (LightType != LIGHT_TYPE_DIRECTIONAL)
	{
		LightAttenuation = GetLocalLightAttenuation(TranslatedWorldPosition, LightData, ToLight, L);
		if (LightData.bRectLight)
		{
			FRect Rect = GetRect(ToLight, LightData);
			LightAttenuation *= IntegrateLight(Rect);
		}
		else
		{
			FCapsuleLight Capsule = GetCapsule(ToLight, LightData);
			Capsule.DistBiasSqr = 0;
			LightAttenuation *= IntegrateLight(Capsule, LightData.bInverseSquared);
		}
	}

	float HardSurfaceShadowFactor = ComputeHardSurfaceShadowFactor(TranslatedWorldPosition, LightData, LightType);
	Inscattering = LightData.Color * LightAttenuation * HardSurfaceShadowFactor;
	if (any(Inscattering > 0.0) && bApplyShadowTransmittance)
	{
		float3 BiasedWorldPosition = WorldPosition + L * WorldShadowBias;
		Inscattering *= ComputeTransmittance(BiasedWorldPosition, ToLight, MaxStepCount);
	}
#endif // DIM_USE_INSCATTERING_VOLUME

	return Inscattering;
}

void RayMarchEmissionAbsorption(
	inout FRayMarchingContext RayMarchingContext,
	uint StepCount,
	inout float3 Radiance,
	inout float3 Transmittance
)
{
	for (uint StepIndex = 0; StepIndex < StepCount; ++StepIndex)
	{
		float LocalHitT = RayMarchingContext.LocalRayTMin + RayMarchingContext.StepSize * (RayMarchingContext.Jitter + StepIndex);
		float3 LocalPosition = RayMarchingContext.LocalRayOrigin + RayMarchingContext.LocalRayDirection * LocalHitT;
		float3 WorldPosition = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection * LocalHitT * RayMarchingContext.LocalToWorldScale;
#if USE_ANALYTIC_DERIVATIVES
		float3 WorldPosition_DDX = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDX * LocalHitT * RayMarchingContext.LocalToWorldScale;
		float3 WorldPosition_DDY = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDY * LocalHitT * RayMarchingContext.LocalToWorldScale;
		WorldPosition_DDX -= WorldPosition;
		WorldPosition_DDY -= WorldPosition;
#else
		float3 WorldPosition_DDX = 0.0;
		float3 WorldPosition_DDY = 0.0;
#endif // USE_ANALYTIC_DERIVATIVES

		FVolumeSampleContext SampleContext = CreateVolumeSampleContext(LocalPosition, WorldPosition, WorldPosition_DDX, WorldPosition_DDY, RayMarchingContext.MipLevel);
		float3 Extinction = SampleExtinction(SampleContext);

		float3 Emission = SampleEmission(SampleContext);
		Radiance += Emission * RayMarchingContext.StepSize * Transmittance;

		// Accumulate transmittance for the next evaluation
		Transmittance *= exp(-Extinction * RayMarchingContext.StepSize);
	}
}

void RayMarchSingleScattering(
	inout FRayMarchingContext RayMarchingContext,
#if USE_CAMERA_AVSM
	inout FAVSM_Sampler4 TransmittanceSampler,
#endif
	FDeferredLightData LightData,
	uint LightType,
	uint StepCount,
	inout float LocalScatterTMin,
	inout float3 Radiance,
	inout float3 Transmittance
)
{
	LocalScatterTMin = RayMarchingContext.LocalRayTMax;
#if APPLY_FOG_INSCATTERING != APPLY_FOG_INSCATTERING_OFF
	float3 FogRadiance = 0;
	float3 PrevTransmittance = Transmittance;
	float4 PrevCombinedFog = 0;
#endif // #if APPLY_FOG_INSCATTERING

	float WorldShadowBias = CalcShadowBias();
	//float WorldShadowBias = View.GeneralPurposeTweak;
	float StepSize = RayMarchingContext.StepSize;
	float JitterSize = StepSize * RayMarchingContext.Jitter;
	// Reuse jitter as random sample
	float RandomSample = RayMarchingContext.Jitter;

	for (uint StepIndex = 0; StepIndex < StepCount; ++StepIndex)
	{
		// Correct the final step size
		float LocalHitT = RayMarchingContext.LocalRayTMin + RayMarchingContext.StepSize * (RayMarchingContext.Jitter + StepIndex);
		if (StepIndex == StepCount - 1)
		{
			float UnjitteredLocalHitT = LocalHitT - JitterSize;
			StepSize = RayMarchingContext.LocalRayTMax - UnjitteredLocalHitT;
		}

		float WorldHitT = LocalHitT * RayMarchingContext.LocalToWorldScale;
		float3 LocalPosition = RayMarchingContext.LocalRayOrigin + RayMarchingContext.LocalRayDirection * LocalHitT;
		float3 WorldPosition = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection * WorldHitT;
#if IS_OFFLINE_RENDER
		float3 FilterWidth = 1.0;
		float Rand = RandomSequence_GenerateSample1D(RayMarchingContext.RandSequence);
		int StochasticFilteringMode = GetStochasticFilteringMode();
		FVolumeSampleContext SampleContext = CreateVolumeSampleContext(LocalPosition, WorldPosition, FilterWidth, RayMarchingContext.MipLevel, Rand, StochasticFilteringMode);
#else
	#if USE_ANALYTIC_DERIVATIVES
		float3 WorldPosition_DDX = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDX * WorldHitT;
		float3 WorldPosition_DDY = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDY * WorldHitT;
		WorldPosition_DDX -= WorldPosition;
		WorldPosition_DDY -= WorldPosition;
	#else
		float3 WorldPosition_DDX = 0.0;
		float3 WorldPosition_DDY = 0.0;
	#endif // USE_ANALYTIC_DERIVATIVES

		FVolumeSampleContext SampleContext = CreateVolumeSampleContext(LocalPosition, WorldPosition, WorldPosition_DDX, WorldPosition_DDY, RayMarchingContext.MipLevel);
#endif // IS_OFFLINE_RENDER

		if (RayMarchingContext.bApplyEmissionAndTransmittance)
		{
			float3 Emission = SampleEmission(SampleContext);
			Radiance += Emission * StepSize * Transmittance;

#if DIM_USE_LUMEN_GI
			FTwoBandSHVectorRGB TranslucencyGISH = GetTranslucencyGIVolumeLighting(DFPromote(WorldPosition), PrimaryView.WorldToClip, true);

			FTwoBandSHVector RotatedHGZonalHarmonic;
			float3 CameraVector = normalize(RayMarchingContext.WorldRayDirection);
			float PhaseG = 0.0;
			RotatedHGZonalHarmonic.V = float4(1.0f, CameraVector.y, CameraVector.z, CameraVector.x) * float4(1.0f, PhaseG, PhaseG, PhaseG);
			float3 IndirectInscattering = max(DotSH(TranslucencyGISH, RotatedHGZonalHarmonic), 0) / PI;

			float3 Extinction = SampleExtinction(SampleContext);
			float3 Albedo = SampleAlbedo(SampleContext);
			float3 ScatteringCoefficient = Albedo * Extinction;
			float AmbientOcclusion = EvalAmbientOcclusion(WorldPosition);

			// Note: Phase is accounted for in spherical hamonic calculation
			Radiance += IndirectInscattering * AmbientOcclusion * ScatteringCoefficient * Transmittance * StepSize;
#endif // DIM_USE_LUMEN_GI
		}

		float3 Extinction = SampleExtinction(SampleContext);
		if (RayMarchingContext.bApplyDirectLighting && (any(Extinction > 0)))
		{
			float3 RadianceSample = 0;

			// Evaluate in-scattering
			float3 Albedo = SampleAlbedo(SampleContext);
			if (any(Albedo > 0.0))
			{
				float3 Inscattering = ComputeInscattering(WorldPosition, LightData, LightType, RayMarchingContext.MaxStepCount, WorldShadowBias, RayMarchingContext.bApplyShadowTransmittance);

				float3 ScatteringCoefficient = Albedo * Extinction;
				float IsotropicPhase = 1.0 / (4.0 * PI);
				RadianceSample = Inscattering * ScatteringCoefficient * IsotropicPhase * Transmittance * StepSize;
			}

			// Stochastically decide to record scatter position
			if (any(Transmittance < RandomSample))
			{
				LocalScatterTMin = min(LocalHitT, LocalScatterTMin);
			}

#if APPLY_FOG_INSCATTERING == APPLY_FOG_INSCATTERING_REFERENCE
			{
				float WorldRayT = LocalHitT * RayMarchingContext.LocalToWorldScale;
				float3 CameraRelative_WorldPosition = RayMarchingContext.WorldRayDirection * WorldRayT;
				float4 HeightFog = float4(0, 0, 0, 1);
				if (ShouldApplyHeightFog())
				{
					HeightFog = CalculateHeightFog(CameraRelative_WorldPosition);
				}

				float4 CombinedFog = HeightFog;
				if (ShouldApplyVolumetricFog() && FogStruct.ApplyVolumetricFog > 0)
				{
					float3 WorldPosition = CameraRelative_WorldPosition + RayMarchingContext.WorldRayOrigin;
					const uint EyeIndex = 0;
					float3 VolumeUV = ComputeVolumeUV_DEPRECATED(WorldPosition, DFHackToFloat(PrimaryView.WorldToClip));
					CombinedFog = CombineVolumetricFog(HeightFog, VolumeUV, EyeIndex, WorldRayT);
				}

				// Attenuate RadianceSample
				RadianceSample *= CombinedFog.a;

				// Add FogRadiance
				float3 DeltaTransmittance = clamp(Transmittance / PrevTransmittance, 0.0, 1.0);
				float3 DeltaFogRadiance = CombinedFog.rgb - PrevCombinedFog.rgb;
				FogRadiance += DeltaFogRadiance * DeltaTransmittance;

				PrevCombinedFog = CombinedFog;
			}
#endif // APPLY_FOG_INSCATTERING_REFERENCE

			// Accumulate radiance
			Radiance += RadianceSample;
		}

#if USE_CAMERA_AVSM
		//Transmittance = AVSM_Sampler4_Eval_NoInterpolation(TransmittanceSampler, WorldHitT);
		Transmittance = AVSM_Sampler4_Eval(TransmittanceSampler, WorldHitT);
#else
		// Accumulate transmittance for the next evaluation
		Transmittance *= exp(-Extinction * StepSize);
#endif

		float Epsilon = 1.0e-7;
		if (all(Transmittance < Epsilon))
		{
			Transmittance = 0.0;
			break;
		}

		LocalHitT += StepSize;
	}

#if APPLY_FOG_INSCATTERING == APPLY_FOG_INSCATTERING_STOCHASTIC
	if (LocalScatterTMin < RayMarchingContext.LocalRayTMax)
	{
		float WorldRayT = LocalScatterTMin * RayMarchingContext.LocalToWorldScale;

		float3 CameraRelative_WorldPosition = RayMarchingContext.WorldRayDirection * WorldRayT;
		float4 HeightFog = float4(0, 0, 0, 1);
		if (ShouldApplyHeightFog())
		{
			HeightFog = CalculateHeightFog(CameraRelative_WorldPosition);
		}

		float4 CombinedFog = HeightFog;
		if (ShouldApplyVolumetricFog() && FogStruct.ApplyVolumetricFog > 0)
		{
			float3 WorldPosition = CameraRelative_WorldPosition + RayMarchingContext.WorldRayOrigin;
			const uint EyeIndex = 0;
			float3 VolumeUV = ComputeVolumeUV_DEPRECATED(WorldPosition, DFHackToFloat(PrimaryView.WorldToClip));
			CombinedFog = CombineVolumetricFog(HeightFog, VolumeUV, EyeIndex, WorldRayT);
		}

		// Attenuate Radiance by fog
		Radiance *= CombinedFog.a;
		FogRadiance = CombinedFog.rgb;
	}
#endif // APPLY_FOG_INSCATTERING

#if APPLY_FOG_INSCATTERING != APPLY_FOG_INSCATTERING_OFF
	// Simplifying terms to avoid vector division:
	// float3 DeltaTransmittance = clamp(Transmittance / PrevTransmittance, 0.0, 1.0);
	// float3 DeltaOpacity = 1.0 - DeltaTransmittance;
	// float3 FogAlpha = PrevTransmittance * DeltaOpacity;
	float3 FogAlpha = clamp(PrevTransmittance - Transmittance, 0.0, 1.0);

	// Fogging in-scattering contribution is not currently attenuated by Heterogeneous Volumes
	// for elements behind the subject. To prevent doubled contribution of in-scattering for 
	// regions of low density (eg: silhouette edges), blending is interpreted as alpha-masking instead.
	Radiance += FogRadiance.rgb * FogAlpha;
#endif // APPLY_FOG_INSCATTERING
}

void RayMarchSingleScatteringReferenceFastPath(
	inout FRayMarchingContext RayMarchingContext,
#if USE_CAMERA_AVSM
	inout FAVSM_Sampler4 TransmittanceSampler,
#endif
	uint StepCount,
	inout float3 Radiance,
	inout float3 Transmittance
)
{
	for (uint StepIndex = 0; StepIndex < StepCount; ++StepIndex)
	{
		float StepSize = RayMarchingContext.StepSize;
		float LocalHitT = RayMarchingContext.LocalRayTMin + RayMarchingContext.StepSize * (RayMarchingContext.Jitter + StepIndex);
		float WorldHitT = LocalHitT * RayMarchingContext.LocalToWorldScale;
		float3 LocalPosition = RayMarchingContext.LocalRayOrigin + RayMarchingContext.LocalRayDirection * LocalHitT;
		float3 WorldPosition = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection * WorldHitT;
#if USE_ANALYTIC_DERIVATIVES
		float3 WorldPosition_DDX = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDX * WorldHitT;
		float3 WorldPosition_DDY = RayMarchingContext.WorldRayOrigin + RayMarchingContext.WorldRayDirection_DDY * WorldHitT;
		WorldPosition_DDX -= WorldPosition;
		WorldPosition_DDY -= WorldPosition;
#else
		float3 WorldPosition_DDX = 0.0;
		float3 WorldPosition_DDY = 0.0;
#endif // USE_ANALYTIC_DERIVATIVES

		FVolumeSampleContext SampleContext = CreateVolumeSampleContext(LocalPosition, WorldPosition, WorldPosition_DDX, WorldPosition_DDY, RayMarchingContext.MipLevel);

		// Radiance from emission
		if (RayMarchingContext.bApplyEmissionAndTransmittance)
		{
			float3 Emission = SampleEmission(SampleContext);
			Radiance += Emission * StepSize * Transmittance;
		}

		// Radiance from in-scattering
		float3 Extinction = SampleExtinction(SampleContext);
		if (RayMarchingContext.bApplyDirectLighting && (any(Extinction > 0)))
		{
			float3 RadianceSample = 0;

			// Evaluate in-scattering
			float3 Albedo = SampleAlbedo(SampleContext);
			if (any(Albedo > 0.0))
			{
				// Evaluate lighting cache
				float3 LocalBoundsMin = GetLocalBoundsOrigin() - GetLocalBoundsExtent();
				float3 UVW = saturate((LocalPosition - LocalBoundsMin) / (2.0 * GetLocalBoundsExtent()));
				float3 Inscattering = SampleLightingCache(UVW, 0) * View.OneOverPreExposure;

				// Evaluate scattering
				float3 ScatteringCoefficient = Albedo * Extinction;
				float IsotropicPhase = 1.0 / (4.0 * PI);
				RadianceSample = Inscattering * ScatteringCoefficient * IsotropicPhase * Transmittance * StepSize;
			}

			Radiance += RadianceSample;
		}

		// Accumulate transmittance for the next evaluation
#if USE_CAMERA_AVSM
		Transmittance = AVSM_Sampler4_Eval(TransmittanceSampler, WorldHitT);
#else
		Transmittance *= exp(-Extinction * StepSize);
#endif

		float Epsilon = 1.0e-7;
		if (all(Transmittance < Epsilon))
		{
			Transmittance = 0.0;
			break;
		}
	}
}
