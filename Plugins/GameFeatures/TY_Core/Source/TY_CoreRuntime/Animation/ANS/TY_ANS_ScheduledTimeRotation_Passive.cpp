#include "TY_ANS_ScheduledTimeRotation_Passive.h"

#include "Character/LyraCharacter.h"
#include "AnimNotifyComponent/TYAnimNotifyComponent.h"
#include "FindTargetComponent/TYFindTargetComponent.h"
#include "Kismet/KismetMathLibrary.h"

void UTY_ANS_ScheduledTimeRotation_Passive::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
                                                 const FAnimNotifyEventReference& EventReference)
{
	AActor* OwnerActor = MeshComp->GetOwner();
	ALyraCharacter* LyraCharacter = CastChecked<ALyraCharacter>(OwnerActor);
	UTYAnimNotifyComponent* AnimNotifyComponent = LyraCharacter->GetComponentByClass<UTYAnimNotifyComponent>();
	UTYFindTargetComponent* FindTargetComponent = LyraCharacter->GetComponentByClass<UTYFindTargetComponent>();

	ALyraCharacter* TargetController = CastChecked<ALyraCharacter>(FindTargetComponent->Controller);
	AnimNotifyComponent->STRP_TargetActor = TargetController;
	AnimNotifyComponent->STRP_TargetRotation =
		UKismetMathLibrary::FindLookAtRotation(LyraCharacter->GetActorLocation(), AnimNotifyComponent->STRP_TargetActor->GetActorLocation()).Yaw;
	AnimNotifyComponent->STRP_SourceRotation = LyraCharacter->GetActorRotation().Yaw;
	AnimNotifyComponent->STRP_TotalDuration = TotalDuration;
}

void UTY_ANS_ScheduledTimeRotation_Passive::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
                                                const FAnimNotifyEventReference& EventReference)
{
	AActor* OwnerActor = MeshComp->GetOwner();
	ALyraCharacter* LyraCharacter = CastChecked<ALyraCharacter>(OwnerActor);
	UTYAnimNotifyComponent* AnimNotifyComponent = LyraCharacter->GetComponentByClass<UTYAnimNotifyComponent>();

	AnimNotifyComponent->STRP_PassedTime += FrameDeltaTime;
	float CompletePercent = AnimNotifyComponent->STRP_PassedTime / AnimNotifyComponent->STRP_TotalDuration;

	if (AnimNotifyComponent->STRP_TargetRotation >= 0.f)
	{
		if (AnimNotifyComponent->STRP_SourceRotation > AnimNotifyComponent->STRP_TargetRotation)
		{
			LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f,
			                                                                AnimNotifyComponent->STRP_SourceRotation - CompletePercent * (AnimNotifyComponent->
				                                                                STRP_SourceRotation - AnimNotifyComponent->STRP_TargetRotation)));
		}
		else
		{
			if (AnimNotifyComponent->STRP_SourceRotation < AnimNotifyComponent->STRP_TargetRotation - 180.f)
			{
				LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f,
				                                                                AnimNotifyComponent->STRP_SourceRotation - CompletePercent *
				                                                                UKismetMathLibrary::Percent_FloatFloat(
					                                                                180.f - AnimNotifyComponent->STRP_SourceRotation + 180.f - AnimNotifyComponent->
					                                                                STRP_TargetRotation, 180.f)));
			}
			else
			{
				LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f,
				                                                                AnimNotifyComponent->STRP_SourceRotation + CompletePercent * (AnimNotifyComponent->
					                                                                STRP_TargetRotation - AnimNotifyComponent->STRP_SourceRotation)));
			}
		}
	}
	else
	{
		if (AnimNotifyComponent->STRP_TargetRotation > AnimNotifyComponent->STRP_SourceRotation)
		{
			LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f,
			                                                                AnimNotifyComponent->STRP_SourceRotation + CompletePercent * UKismetMathLibrary::Abs(
				                                                                AnimNotifyComponent->
				                                                                STRP_SourceRotation - AnimNotifyComponent->STRP_TargetRotation)));
		}
		else
		{
			if (AnimNotifyComponent->STRP_SourceRotation > AnimNotifyComponent->STRP_TargetRotation + 180.f)
			{
				LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f,
				                                                                AnimNotifyComponent->STRP_SourceRotation + CompletePercent *
				                                                                UKismetMathLibrary::Percent_FloatFloat(
					                                                                UKismetMathLibrary::Abs(180.f - AnimNotifyComponent->STRP_SourceRotation) +
					                                                                180.f - UKismetMathLibrary::Abs(AnimNotifyComponent->
						                                                                STRP_TargetRotation), 180.f)));
			}
			else
			{
				LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f,
				                                                                AnimNotifyComponent->STRP_SourceRotation - CompletePercent * UKismetMathLibrary::Abs(
					                                                                AnimNotifyComponent->STRP_SourceRotation - AnimNotifyComponent->STRP_TargetRotation)));
			}
		}
	}
}

void UTY_ANS_ScheduledTimeRotation_Passive::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference)
{
	if (!UnderAttact)
	{
		return;
	}

	AActor* OwnerActor = MeshComp->GetOwner();
	ALyraCharacter* LyraCharacter = CastChecked<ALyraCharacter>(OwnerActor);
	UTYAnimNotifyComponent* AnimNotifyComponent = LyraCharacter->GetComponentByClass<UTYAnimNotifyComponent>();

	if (ForceFaceTargetAsANSEnd)
	{
		LyraCharacter->SetActorRotation(UKismetMathLibrary::MakeRotator(0.f, 0.f, AnimNotifyComponent->STRP_TargetRotation));
	}

	// Clear Component Value
	AnimNotifyComponent->STRP_TargetRotation = 0.f;
	AnimNotifyComponent->STRP_SourceRotation = 0.f;
	AnimNotifyComponent->STRP_TargetActor = nullptr;
	AnimNotifyComponent->STRP_PassedTime = 0.f;
	AnimNotifyComponent->STRP_TotalDuration = 0.f;
}
