// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "ActorCacheSubsystem.generated.h"

/**
 *  一个用来缓存当前所有的有含义的Actor的subsystem，包括敌兵，玩家
 */
UCLASS()
class TY_CORERUNTIME_API UActorCacheSubsystem : public UWorldSubsystem
{
	GENERATED_BODY()

public:
	// 添加一个actor到缓存
	void AddActor(AActor* Actor);

	// 从缓存中移除一个actor
	void RemoveActor(AActor* Actor);

	// 获取所有缓存的actor
	const TArray<AActor*>& GetAllActors() const;

private:
	UPROPERTY()
	// 缓存的actor数组
	TArray<TObjectPtr<AActor>> CachedActors;
};
