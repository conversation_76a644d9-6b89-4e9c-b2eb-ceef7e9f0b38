// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AnimToTextureDataAsset.h"
#include "AnimToTextureDataAsset.h"
#include "Engine/DataAsset.h"
#include "VATBatchConfig.generated.h"

/**
 * 
 */
UCLASS(Blueprintable, BlueprintType)
class TY_EXPERIENCES_STGAMEEDITOR_API UVATBatchConfig : public UDataAsset
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UAnimToTextureDataAsset*> BatchDataAssets;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAnimToTextureAnimSequenceInfo> AnimSequences;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float SampleRate;	
	
	
};
