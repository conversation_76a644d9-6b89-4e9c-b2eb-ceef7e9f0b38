// Copyright (C) Developed by Pask, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 

#pragma once

#include "CoreMinimal.h"
#include "GameplayEffect.h"
#include "STStunComponent.generated.h"

UENUM(BlueprintType)
enum EStunStage : int
{
	SS_None = 0,
	SS_StayMax = 1,
	//眩晕值累计到最大时触发
	SS_RecoverFromStun = 2,
	//长时间未受击触发
	SS_RecoverFromNoHit,
};

/**
 * 
 */
UCLASS( ClassGroup=(ST), meta=(BlueprintSpawnableComponent, VisibleAnywhere) )
class TY_EXPERIENCES_STGAMERUNTIME_API USTStunComponent : public UActorComponent
{
	GENERATED_BODY()
public:
	USTStunComponent();
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", DisplayName="延迟多久触发恢复眩晕值的GE")
	float DelayTriggerStunGETime = 0.3f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", DisplayName="恢复眩晕值的GE")
	TSubclassOf<UGameplayEffect> DelayTriggerStunGE;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", DisplayName="未受击自动恢复的间隔")
	float NoHitAutoRecoverTime = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", DisplayName="未受击自动恢复的使用的GE")
	TSubclassOf<UGameplayEffect> NoHitAutoRecoverGE;
	
public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	//受击时触发是否进眩晕的检查
	void CheckTriggerStun(AActor* Attacker);

	UFUNCTION(BlueprintImplementableEvent, DisplayName="OnTriggerStun")
	void K2_OnTriggerStun(AActor* Attacker);

	UFUNCTION(BlueprintImplementableEvent, DisplayName="OnStunRecover")
	void K2_OnStunRecover();

private:
	float DelayTimer;
	EStunStage CurrentStage = EStunStage::SS_None;

private:
	void CheckStage(float DeltaTime);
	void GotoStage(EStunStage NewStage, AActor* Attacker = nullptr);
	
	void BeginStayMax(AActor* Attacker);
	void EndStayMax();
	void BeginRecoverFromStun();
	void EndRecoverFromStun();
	void BeginRecoverFromNoHit();
	void EndRecoverFromNoHit();
};



