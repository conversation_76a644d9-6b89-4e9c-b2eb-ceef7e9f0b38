// Copyright (C) Developed by <PERSON><PERSON>, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 

#pragma once

#include "CoreMinimal.h"
#include "Actor/STProjectileBase.h"
#include "AnimNotify/STAnimNotifyDuration.h"
#include "STSpawnBulletDuration.generated.h"


/**
 * 
 */
UCLASS()
class TY_EXPERIENCES_STGAMERUNTIME_API USTSpawnBulletDuration : public USTAnimNotifyDuration
{
	GENERATED_BODY()


protected:
	//仅用来做命中效果，通用一些，和伤害拆分开
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	TSubclassOf<ASTProjectileBase> BulletBluePrintClassToSpawn;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	int SpawnNumber = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	float Damage;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	int StiffLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	float Speed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	float MinDeviationAngle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	float MaxDeviationAngle;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", meta = (AnimNotifyBoneName = "true"))
	FName SpawnerSocket;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST" )
	float AutoDestroyAfterCollision;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST" )
	float LifeSpan;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", DisplayName = "技能效果，作用在受击者身上")
	TArray<TSubclassOf<UGameplayEffect>> GE_DoDamageActions;

public:
	virtual void OnBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void OnEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	
};
