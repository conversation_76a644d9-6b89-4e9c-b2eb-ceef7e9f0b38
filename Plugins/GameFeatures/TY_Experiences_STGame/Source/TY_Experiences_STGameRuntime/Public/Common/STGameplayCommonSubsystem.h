// Copyright (C) Developed by <PERSON><PERSON>, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "STGameplayCommonSubsystem.generated.h"

class USTPostProcessManager;
/**
 * 
 */
UCLASS()
class TY_EXPERIENCES_STGAMERUNTIME_API USTGameplayCommonSubsystem : public UGameInstanceSubsystem, public FTickableGameObject
{
	GENERATED_BODY()

private:
	USTPostProcessManager* PostProcessManager;
	
public:
	UFUNCTION(BlueprintCallable, Category = ST)
	FORCEINLINE void RegisterPostProcessManager(USTPostProcessManager* Input)
	{
		PostProcessManager = Input;
	}
	
	UFUNCTION(BlueprintCallable, Category = ST)
	FORCEINLINE USTPostProcessManager* GetPostProcessManager()
	{
		return PostProcessManager;
	}

private:
	// FTickableGameObject interface
	virtual void Tick(float DeltaTime) override;
	virtual bool IsTickable() const override { return true; }
	virtual ETickableTickType GetTickableTickType() const override { return ETickableTickType::Always; }
	virtual TStatId GetStatId() const override { RETURN_QUICK_DECLARE_CYCLE_STAT(USTGameplayCommonSubsystem, STATGROUP_Tickables); }
	virtual UWorld* GetTickableGameObjectWorld() const override { return GetWorld(); }
	// ~FTickableGameObject interface
};
