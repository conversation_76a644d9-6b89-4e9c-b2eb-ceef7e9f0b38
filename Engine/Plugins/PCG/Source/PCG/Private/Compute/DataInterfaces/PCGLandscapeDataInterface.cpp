// Copyright Epic Games, Inc. All Rights Reserved.

#include "PCGLandscapeDataInterface.h"

#include "PCGComponent.h"
#include "PCGData.h"
#include "Compute/PCGDataBinding.h"
#include "Data/PCGLandscapeData.h"

#include "EngineUtils.h"
#include "GlobalRenderResources.h"
#include "Landscape.h"
#include "LandscapeHeightfieldCollisionComponent.h"
#include "LandscapeInfo.h"
#include "RenderGraphBuilder.h"
#include "RenderResource.h"
#include "RHIStaticStates.h"
#include "RHIUtilities.h"
#include "ShaderCompilerCore.h"
#include "ShaderCore.h"
#include "ShaderParameterMetadataBuilder.h"
#include "ComputeFramework/ShaderParamTypeDefinition.h"
#include "Containers/ResourceArray.h"
#include "Misc/LargeWorldRenderPosition.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(PCGLandscapeDataInterface)

#define LOCTEXT_NAMESPACE "UPCGLandscapeDataInterface"

/** RenderResource used to hold textures generated by this DI, pulled from the collision geometry of the terrain. */
class FPCGLandscapeTextureResource : public FRenderResource
{
public:
	FPCGLandscapeTextureResource(const FIntPoint& InCellCount): CellCount(InCellCount) {}
	virtual ~FPCGLandscapeTextureResource() = default;

	FPCGLandscapeTextureResource() = delete;
	FPCGLandscapeTextureResource(const FPCGLandscapeTextureResource&) = delete;
	FPCGLandscapeTextureResource(const FPCGLandscapeTextureResource&&) = delete;

	virtual void InitRHI(FRHICommandListBase& RHICmdList) override
	{
		if (HeightBulkData.DataSize > 0)
		{
			HeightTexture.Initialize(TEXT("FLandscapeTextureResource_HeightTexture"), sizeof(float), CellCount.X, CellCount.Y, EPixelFormat::PF_R32_FLOAT, FTextureReadBuffer2D::DefaultTextureInitFlag, &HeightBulkData);
		}

		ReleaseSourceData();
	}

	virtual void ReleaseRHI() override { HeightTexture.Release(); }
	void ReleaseSourceData() { HeightValues.Empty(); }
	FRHITexture* GetHeightTexture() const { return HeightTexture.Buffer; }
	FIntPoint GetDimensions() const { return CellCount; }

	TArray<float>& EditHeightValues(int32 SampleCount)
	{
		const float DefaultHeight = 0.0f;
		HeightValues.Reset(SampleCount);
		HeightValues.Init(DefaultHeight, SampleCount);
		HeightBulkData.Init(HeightValues.GetData(), HeightValues.Num() * HeightValues.GetTypeSize());

		return HeightValues;
	}

private:
	FTextureReadBuffer2D HeightTexture;
	FIntPoint CellCount;

	TArray<float> HeightValues;

	struct FTextureBulkData : public FResourceBulkDataInterface
	{
		void Init(const void* InData, uint32 InDataSize) { Data = InData; DataSize = InDataSize; }
		void Clear() { Data = nullptr; DataSize = 0; }
		virtual const void* GetResourceBulkData() const override { return Data; }
		virtual uint32 GetResourceBulkDataSize() const override { return DataSize; }
		virtual void Discard() override { }
		const void* Data = nullptr;
		uint32 DataSize = 0;
	};

	FTextureBulkData* GetHeightBulkData() { return HeightBulkData.DataSize > 0 ? &HeightBulkData : nullptr; }
	FTextureBulkData HeightBulkData;
};

/** Manages the resources created by this DI. */
class FPCGLandscapeResource
{
public:
	struct FResourceKey
	{
		TWeakObjectPtr<const ALandscape> Source = nullptr;
		TArray<FIntPoint> CapturedRegions;
		FIntPoint MinCaptureRegion = FIntPoint(ForceInitToZero);
		FIntPoint MaxCaptureRegion = FIntPoint(ForceInitToZero);
		bool bIncludesHeight = false;
	};

	FPCGLandscapeResource() {}
	FPCGLandscapeResource(const FResourceKey& InKey);
	~FPCGLandscapeResource();

	FPCGLandscapeTextureResource* LandscapeTextures = nullptr;
	FVector3f LandscapeLWCTile = FVector3f::ZeroVector;
	FMatrix ActorToWorldTransform = FMatrix::Identity;
	FMatrix WorldToActorTransform = FMatrix::Identity;
	FVector4 UvScaleBias = FVector4(1.0f, 1.0f, 0.0f, 0.0f);
	FIntPoint CellCount = FIntPoint(ForceInitToZero);
	FVector2D TextureWorldGridSize = FVector2D(1.0f, 1.0f);

private:
	FResourceKey ResourceKey;
};

FPCGLandscapeResource::FPCGLandscapeResource(const FResourceKey& InKey)
	: ResourceKey(InKey)
{
	const ULandscapeInfo* LandscapeInfo = ResourceKey.Source->GetLandscapeInfo();
	if (!LandscapeInfo)
	{
		return;
	}

	const int32 ComponentQuadCount = ResourceKey.Source->ComponentSizeQuads;
	const FIntPoint RegionSpan = ResourceKey.MaxCaptureRegion - ResourceKey.MinCaptureRegion + FIntPoint(1, 1);
	const FIntPoint CaptureQuadSpan = RegionSpan * ComponentQuadCount;
	const FIntPoint CaptureVertexSpan = CaptureQuadSpan + FIntPoint(1, 1);
	const int32 SampleCount = CaptureVertexSpan.X * CaptureVertexSpan.Y;

	LandscapeTextures = new FPCGLandscapeTextureResource(CaptureVertexSpan);

	TArray<float>* HeightValues = ResourceKey.bIncludesHeight ? &LandscapeTextures->EditHeightValues(SampleCount) : nullptr;

	const FIntPoint RegionVertexBase = ResourceKey.MinCaptureRegion * ComponentQuadCount;

	for (const FIntPoint& Region : ResourceKey.CapturedRegions)
	{
		auto FoundCollisionComponent = LandscapeInfo->XYtoCollisionComponentMap.Find(Region);
		check(FoundCollisionComponent);

		if (FoundCollisionComponent)
		{
			if (const ULandscapeHeightfieldCollisionComponent* CollisionComponent = *FoundCollisionComponent)
			{
				if (HeightValues)
				{
					const FIntPoint SectionBase = (Region - ResourceKey.MinCaptureRegion) * ComponentQuadCount;
					CollisionComponent->FillHeightTile(*HeightValues, SectionBase.X + SectionBase.Y * CaptureVertexSpan.X, CaptureVertexSpan.X);
				}
			}
		}
	}

	// Number of cells that are represented in our heights array.
	CellCount = CaptureVertexSpan;

	// Mapping to get the UV from 'cell space' which is relative to the entire terrain (not just the captured regions).
	FVector2D UvScale(1.0f / CaptureVertexSpan.X, 1.0f / CaptureVertexSpan.Y);

	UvScaleBias = FVector4(
		UvScale.X,
		UvScale.Y,
		(0.5f - RegionVertexBase.X) * UvScale.X,
		(0.5f - RegionVertexBase.Y) * UvScale.Y);

	FTransform LandscapeTransform = ResourceKey.Source->GetTransform();
	FLargeWorldRenderPosition LandscapeTransformOrigin(LandscapeTransform.GetLocation());

	LandscapeLWCTile = LandscapeTransformOrigin.GetTile();
	LandscapeTransform.SetLocation(FVector(LandscapeTransformOrigin.GetOffset()));

	ActorToWorldTransform = LandscapeTransform.ToMatrixWithScale();
	WorldToActorTransform = ActorToWorldTransform.Inverse();
	TextureWorldGridSize = FVector2D(ResourceKey.Source->GetTransform().GetScale3D());
}

FPCGLandscapeResource::~FPCGLandscapeResource()
{
	if (LandscapeTextures)
	{
		// Release the LandscapeTextures resource handle on the RHI and clear the pointer.

		ENQUEUE_RENDER_COMMAND(BeginDestroyCommand)([RT_Resource = LandscapeTextures](FRHICommandListImmediate& RHICmdList)
		{
			check(RT_Resource);
			RT_Resource->ReleaseResource();

			// On some RHIs textures will push data on the RHI thread
			// Therefore we are not 'released' until the RHI thread has processed all commands
			RHICmdList.EnqueueLambda([RT_Resource](FRHICommandListImmediate& RHICmdList)
			{
				delete RT_Resource;
			});
		});

		LandscapeTextures = nullptr;
	}
}

void UPCGLandscapeDataInterface::GetSupportedInputs(TArray<FShaderFunctionDefinition>& OutFunctions) const
{
	OutFunctions.AddDefaulted_GetRef()
		.SetName(TEXT("GetHeight"))
		.AddReturnType(EShaderFundamentalType::Float)
		.AddParam(EShaderFundamentalType::Float, 3);

	OutFunctions.AddDefaulted_GetRef()
		.SetName(TEXT("GetNormal"))
		.AddReturnType(EShaderFundamentalType::Float, 3)
		.AddParam(EShaderFundamentalType::Float, 3);

	// TODO: Color, PhysicalMaterialIndex, etc.
}

BEGIN_SHADER_PARAMETER_STRUCT(FPCGLandscapeDataInterfaceParameters,)
	SHADER_PARAMETER_TEXTURE(Texture2D, HeightTexture)
	SHADER_PARAMETER_SAMPLER(SamplerState, HeightTextureSampler)
	SHADER_PARAMETER(FVector3f, HeightTextureLWCTile)
	SHADER_PARAMETER(FMatrix44f, HeightTextureWorldToUvTransform)
	SHADER_PARAMETER(FVector4f, HeightTextureUvScaleBias)
	SHADER_PARAMETER(FVector2f, HeightTextureWorldGridSize)
	SHADER_PARAMETER(FVector3f, SystemLWCTile)
END_SHADER_PARAMETER_STRUCT()

void UPCGLandscapeDataInterface::GetShaderParameters(TCHAR const* UID, FShaderParametersMetadataBuilder& InOutBuilder, FShaderParametersMetadataAllocations& InOutAllocations) const
{
	InOutBuilder.AddNestedStruct<FPCGLandscapeDataInterfaceParameters>(UID);
}

TCHAR const* UPCGLandscapeDataInterface::TemplateFilePath = TEXT("/Plugin/PCG/Private/PCGLandscapeDataInterface.ush");

TCHAR const* UPCGLandscapeDataInterface::GetShaderVirtualPath() const
{
	return TemplateFilePath;
}

void UPCGLandscapeDataInterface::GetShaderHash(FString& InOutKey) const
{
	GetShaderFileHash(TemplateFilePath, EShaderPlatform::SP_PCD3D_SM5).AppendString(InOutKey);
}

void UPCGLandscapeDataInterface::GetHLSL(FString& OutHLSL, FString const& InDataInterfaceName) const
{
	TMap<FString, FStringFormatArg> TemplateArgs =
	{
		{ TEXT("DataInterfaceName"), InDataInterfaceName },
	};

	FString TemplateFile;
	if (ensure(LoadShaderSourceFile(TemplateFilePath, EShaderPlatform::SP_PCD3D_SM5, &TemplateFile, nullptr)))
	{
		OutHLSL += FString::Format(*TemplateFile, TemplateArgs);
	}
}

UComputeDataProvider* UPCGLandscapeDataInterface::CreateDataProvider(TObjectPtr<UObject> InBinding, uint64 InInputMask, uint64 InOutputMask) const
{
	TRACE_CPUPROFILER_EVENT_SCOPE(UPCGLandscapeDataInterface::CreateDataProvider);
	UPCGDataBinding* Binding = CastChecked<UPCGDataBinding>(InBinding);
	check(Binding->SourceComponent.IsValid() && Binding->SourceComponent.Get());

	const UPCGComponent* SourceComponent = Binding->SourceComponent.Get();
	const UWorld* World = SourceComponent->GetWorld();
	const FBox ComponentBounds = SourceComponent->GetGridBounds();

	auto TestLandscape = [World, &ComponentBounds](const ALandscape* InLandscape)
	{
		if (InLandscape->GetWorld() == World)
		{
			if (const ULandscapeInfo* LandscapeInfo = InLandscape->GetLandscapeInfo())
			{
				for (const auto& ComponentIt : LandscapeInfo->XYtoCollisionComponentMap)
				{
					if (ComponentBounds.IntersectXY(ComponentIt.Value->Bounds.GetBox()))
					{
						return true;
					}
				}
			}
		}

		return false;
	};

	TObjectPtr<UPCGLandscapeDataProvider> DataProvider = NewObject<UPCGLandscapeDataProvider>();

	// Take any input pin label alias to obtain the data from the input data collection.
	check(!DownstreamInputPinLabelAliases.IsEmpty());
	const TArray<FPCGTaggedData> TaggedDatas = Binding->DataForGPU.InputDataCollection.GetInputsByPin(DownstreamInputPinLabelAliases[0]);

	if (!TaggedDatas.IsEmpty())
	{
		ensure(TaggedDatas.Num() == 1); // There should only be one landscape data
		ensure(TaggedDatas[0].Data->IsA<UPCGLandscapeData>());

		if (const UPCGLandscapeData* LandscapeData = Cast<UPCGLandscapeData>(TaggedDatas[0].Data))
		{
			for (TSoftObjectPtr<ALandscapeProxy> LandscapeProxyPtr : LandscapeData->Landscapes)
			{
				if (ALandscapeProxy* LandscapeProxy = LandscapeProxyPtr.Get())
				{
					if (ALandscape* Landscape = LandscapeProxy->GetLandscapeActor())
					{
						if (TestLandscape(Landscape))
						{
							DataProvider->Initialize(Landscape, ComponentBounds);
							break;
						}
					}
				}
			}
		}
	}

	return DataProvider;
}

void UPCGLandscapeDataProvider::Initialize(ALandscape* InLandscape, const FBox& Bounds)
{
	TRACE_CPUPROFILER_EVENT_SCOPE(UPCGLandscapeDataProvider::Initialize);
	check(IsInGameThread());

	const ULandscapeInfo* LandscapeInfo = InLandscape ? InLandscape->GetLandscapeInfo() : nullptr;

	if (!ensure(LandscapeInfo))
	{
		return;
	}

	// We want to use the bounds of the system to figure out which cells of the landscape that we need to handle.
	const int32 MaxLandscapeRegionCount = LandscapeInfo->XYtoCollisionComponentMap.Num();

	const FVector3f LWCTile(0, 0, 0); // Used to offset in LWC for precision, default to 0-vector for now.
	const FVector LWCTileOffset = FVector(LWCTile) * FLargeWorldRenderScalar::GetTileSize();
	FBox SystemWorldBounds = Bounds;
	SystemWorldBounds.Min += LWCTileOffset;
	SystemWorldBounds.Max += LWCTileOffset;

	const FTransform& LandscapeActorToWorld = InLandscape->LandscapeActorToWorld();
	const FVector SystemMinInLandscape = LandscapeActorToWorld.InverseTransformPosition(SystemWorldBounds.Min);
	const FVector SystemMaxInLandscape = LandscapeActorToWorld.InverseTransformPosition(SystemWorldBounds.Max);

	FBox SystemBoundsInLandscape(
		SystemMinInLandscape.ComponentMin(SystemMaxInLandscape),
		SystemMinInLandscape.ComponentMax(SystemMaxInLandscape));

	// Transform the above box into a range of integers covering the cells of the landscape first clamp it at 0.
	SystemBoundsInLandscape.Min = SystemBoundsInLandscape.Min.ComponentMax(FVector::ZeroVector);
	SystemBoundsInLandscape.Max = SystemBoundsInLandscape.Max.ComponentMax(FVector::ZeroVector);

	// Next rescale based on the quad size.
	const double QuadSizeScaleFactor = 1.0 / ((double)InLandscape->ComponentSizeQuads);
	SystemBoundsInLandscape.Min *= QuadSizeScaleFactor;
	SystemBoundsInLandscape.Max *= QuadSizeScaleFactor;

	// Truncate to integers.
	const FVector MaxIntValue = FVector(double(TNumericLimits<int32>::Max()));
	SystemBoundsInLandscape.Min = SystemBoundsInLandscape.Min.ComponentMin(MaxIntValue);
	SystemBoundsInLandscape.Max = SystemBoundsInLandscape.Max.ComponentMin(MaxIntValue);

	const FIntRect SystemRect = FIntRect(
		FIntPoint(FMath::FloorToInt(SystemBoundsInLandscape.Min.X), FMath::FloorToInt(SystemBoundsInLandscape.Min.Y)),
		FIntPoint(FMath::CeilToInt(SystemBoundsInLandscape.Max.X), FMath::CeilToInt(SystemBoundsInLandscape.Max.Y)));

	// For obnoxiously large system bounds we need to guard against potential overflow on the number of cells.
	const int32 MaxSystemWidth = FMath::Clamp<int32>(SystemRect.Max.X - SystemRect.Min.X, 0, MaxLandscapeRegionCount);
	const int32 MaxSystemHeight = FMath::Clamp<int32>(SystemRect.Max.Y - SystemRect.Min.Y, 0, MaxLandscapeRegionCount);

	const int64 MaxSystemRegionCount64 = int64(MaxSystemWidth) * int64(MaxSystemHeight);
	const int32 MaxSystemRegionCount = int32(FMath::Min<int64>(int64(TNumericLimits<int32>::Max()), MaxSystemRegionCount64));

	const int32 MaxRegionCount = FMath::Min(MaxSystemRegionCount, MaxLandscapeRegionCount);

	FPCGLandscapeResource::FResourceKey Key;
	Key.Source = InLandscape;
	Key.CapturedRegions.Reserve(MaxRegionCount);
	Key.MinCaptureRegion = FIntPoint(TNumericLimits<int32>::Max(), TNumericLimits<int32>::Max());
	Key.MaxCaptureRegion = FIntPoint(TNumericLimits<int32>::Min(), TNumericLimits<int32>::Min());
	Key.bIncludesHeight = true;

	auto AddRegion = [&](const FIntPoint& Region)
	{
		Key.CapturedRegions.Add(Region);
		Key.MinCaptureRegion = Key.MinCaptureRegion.ComponentMin(Region);
		Key.MaxCaptureRegion = Key.MaxCaptureRegion.ComponentMax(Region);
	};

	if (MaxSystemRegionCount > MaxLandscapeRegionCount)
	{
		for (const auto& LandscapeComponent : LandscapeInfo->XYtoCollisionComponentMap)
		{
			if (SystemRect.Contains(LandscapeComponent.Key))
			{
				AddRegion(LandscapeComponent.Key);
			}
		}
	}
	else
	{
		for (int32 GridY = SystemRect.Min.Y; GridY < SystemRect.Max.Y; ++GridY)
		{
			for (int32 GridX = SystemRect.Min.X; GridX < SystemRect.Max.X; ++GridX)
			{
				const FIntPoint CurrentRegion(GridX, GridY);
				if (LandscapeInfo->XYtoCollisionComponentMap.Contains(CurrentRegion))
				{
					AddRegion(CurrentRegion);
				}
			}
		}
	}

	Resource = MakeUnique<FPCGLandscapeResource>(Key);
}

FComputeDataProviderRenderProxy* UPCGLandscapeDataProvider::GetRenderProxy()
{
	FPCGLandscapeDataProviderProxy* Proxy = new FPCGLandscapeDataProviderProxy(Resource.Get());
	return Proxy;
}

bool FPCGLandscapeDataProviderProxy::IsValid(FValidationData const& InValidationData) const
{
	if (InValidationData.ParameterStructSize != sizeof(FParameters))
	{
		return false;
	}

	return true;
}

void FPCGLandscapeDataProviderProxy::GatherDispatchData(FDispatchData const& InDispatchData)
{
	const TStridedView<FParameters> ParameterArray = MakeStridedParameterView<FParameters>(InDispatchData);
	for (int32 InvocationIndex = 0; InvocationIndex < ParameterArray.Num(); ++InvocationIndex)
	{
		FParameters& Parameters = ParameterArray[InvocationIndex];

		// Set Samplers
		FRHISamplerState* BilinearSamplerState = TStaticSamplerState<SF_Bilinear, AM_Clamp, AM_Clamp, AM_Clamp>::GetRHI();
		FRHISamplerState* PointClampedSampler = TStaticSamplerState<SF_Point, AM_Clamp, AM_Clamp, AM_Clamp>::GetRHI();
		Parameters.HeightTextureSampler = RHIPixelFormatHasCapabilities(EPixelFormat::PF_R32_FLOAT, EPixelFormatCapabilities::TextureFilterable) ? BilinearSamplerState : PointClampedSampler;

		// Set Textures
		FRHITexture* HeightTexture = (Resource && Resource->LandscapeTextures) ? Resource->LandscapeTextures->GetHeightTexture() : (FRHITexture*)GBlackTexture->TextureRHI;
		check(HeightTexture);

		// TODO: Bindless resources to handle multiple landscapes
		Parameters.HeightTexture = HeightTexture;

		if (Resource)
		{
			Parameters.HeightTextureLWCTile = Resource->LandscapeLWCTile;
			Parameters.HeightTextureWorldToUvTransform = (FMatrix44f)Resource->WorldToActorTransform;
			Parameters.HeightTextureUvScaleBias = (FVector4f)Resource->UvScaleBias;
			Parameters.HeightTextureWorldGridSize = (FVector2f)Resource->TextureWorldGridSize;
		}
		else
		{
			Parameters.HeightTextureLWCTile = FVector3f::ZeroVector;
			Parameters.HeightTextureWorldToUvTransform = (FMatrix44f)FMatrix::Identity;
			Parameters.HeightTextureUvScaleBias = (FVector4f)FVector4(1.0f, 1.0f, 0.0f, 0.0f);
			Parameters.HeightTextureWorldGridSize = (FVector2f)FVector2D(1.0f, 1.0f);
		}

		// System tile for LWC
		FVector3f LWCTile(0, 0, 0); // Used to offset in LWC for precision, default to 0-vector for now.
		Parameters.SystemLWCTile = LWCTile;
	}
}

void FPCGLandscapeDataProviderProxy::AllocateResources(FRDGBuilder& GraphBuilder, FAllocationData const& InAllocationData)
{
	if (Resource && Resource->LandscapeTextures)
	{
		Resource->LandscapeTextures->InitResource(GraphBuilder.RHICmdList);
	}
}

#undef LOCTEXT_NAMESPACE
