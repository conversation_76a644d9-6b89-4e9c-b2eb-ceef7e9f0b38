// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "STDamageTextComponent.h"
#include "Actor/STCharacterBase.h"
#include "STNumberPopComponent.generated.h"


USTRUCT(BlueprintType)
struct FSTNumberPopRequest
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Number Pops")
	ASTCharacterBase* TargetCharacter;

	// The world location to create the number pop at
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Number Pops")
	FVector WorldLocation;
	
	// The number to display
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Number Pops")
	int32 NumberToDisplay = 0;

	FSTNumberPopRequest()
		: TargetCharacter(nullptr), WorldLocation(ForceInitToZero)
	{
	}
};


UCLASS(Blueprintable)
class TY_EXPERIENCES_STGAMERUNTIME_API USTNumberPopComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	/** Adds a damage number to the damage number list for visualization */
	UFUNCTION(BlueprintCallable, Category="Number Pops")
	void AddNumberPop(const FSTNumberPopRequest& NewRequest);
	
	UPROPERTY(EditDefaultsOnly)
	TSubclassOf<USTDamageTextComponent> DamageTextComponentClass;
};