#include "TroopFormation/STTroopFormationMoveDefineActor.h"
#include "Components/SplineComponent.h"
#include "GameFramework/Actor.h"

ASTTroopFormationMoveDefineActor::ASTTroopFormationMoveDefineActor()
{
    PrimaryActorTick.bCanEverTick = false;

    // 创建并设为 Root
    MoveSpline = CreateDefaultSubobject<USplineComponent>(TEXT("MoveSpline"));
    RootComponent = MoveSpline;

    // 默认将 Spline 设置为在编辑器中可见
#if WITH_EDITORONLY_DATA
    MoveSpline->bEditableWhenInherited = true;
#endif
}

FFormationMoveBatch ASTTroopFormationMoveDefineActor::GenerateMoveCommands(const TArray<AActor*>& Actors) const
{
    FFormationMoveBatch Batch;

    if (!MoveSpline || Actors.Num() == 0)
    {
        return Batch;
    }

    const int32 NumPoints = Actors.Num();
    const float SplineLength = MoveSpline->GetSplineLength();

    // 1. 先生成所有目标点
    TArray<FFormationPoint> TargetPoints;
    TargetPoints.Reserve(NumPoints);

    for (int32 Index = 0; Index < NumPoints; ++Index)
    {
        const float Alpha = (NumPoints == 1) ? 0.5f : static_cast<float>(Index) / static_cast<float>(NumPoints - 1);
        const float Distance = Alpha * SplineLength;

        const FVector Location = MoveSpline->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FRotator Rotation = MoveSpline->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        Rotation += MovePhase.OrientationOffset;

        FFormationPoint Point(Location, Rotation.Quaternion());
        TargetPoints.Add(Point);
    }

    // 2. 按距离匹配Actor到最近的目标点
    TArray<int32> RemainingPointIndices;
    RemainingPointIndices.Reserve(NumPoints);
    for (int32 i = 0; i < NumPoints; ++i)
    {
        RemainingPointIndices.Add(i);
    }

    // 3. 为每个Actor贪心选择最近的可用点
    for (int32 ActorIdx = 0; ActorIdx < Actors.Num(); ++ActorIdx)
    {
        AActor* Actor = Actors[ActorIdx];
        if (!Actor || RemainingPointIndices.Num() == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("GenerateMoveCommands: Actor is null or no remaining points."));
            continue;
        }

        const FVector ActorLocation = Actor->GetActorLocation();

        float ClosestDistSq = TNumericLimits<float>::Max();
        int32 ClosestArrayIdx = INDEX_NONE; // 在 RemainingPointIndices 中的索引位置

        for (int32 RemIdx = 0; RemIdx < RemainingPointIndices.Num(); ++RemIdx)
        {
            int32 PointIdx = RemainingPointIndices[RemIdx];
            float DistSq = FVector::DistSquared(ActorLocation, TargetPoints[PointIdx].Transform.GetLocation());
            if (DistSq < ClosestDistSq)
            {
                ClosestDistSq = DistSq;
                ClosestArrayIdx = RemIdx;
            }
        }

        if (ClosestArrayIdx != INDEX_NONE)
        {
            int32 PointIdx = RemainingPointIndices[ClosestArrayIdx];
            FFormationPoint& SelectedPoint = TargetPoints[PointIdx];

            FFormationMoveCommand Cmd(Actor, SelectedPoint);
            Cmd.MoveSpeed = MovePhase.MoveSpeed;
            Cmd.bBrace = MovePhase.bBrace;
            Cmd.TriggerMontage = MovePhase.TriggerMontage;

            Batch.Commands.Add(Cmd);
            RemainingPointIndices.RemoveAt(ClosestArrayIdx);
        }
    }

    return Batch;
}
