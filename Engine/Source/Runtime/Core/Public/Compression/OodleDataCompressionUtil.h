// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Containers/Array.h"
#include "Containers/ContainersFwd.h"
#include "HAL/Platform.h"
#include "OodleDataCompression.h"

// OodleDataCompressionUtil : utilities for common Unreal actions built on top of OodleDataCompression

/**
* Represents a compressed buffer prefixed with the compressed and decompressed size.
* The compressed size is technically double-specified, but affords writing the buffer
* to disk without any further header. This header is variable sized based on need - 
* 32 bits unless the sizes require it, then its 64 bit.
*
* This is distinct from FCompressedBuffer as it has less overhead, is not intended to
* seamlessly interface with IoStore, and only supports built-in Oodle.
*
* **NOTE** any data that will be staged to a pak/iostore should NOT be compressed, as
* that system will compress for you in a manner appropriate for the platform!
*
* If you just want raw pointers, call OodleDataCompress/Decompress directly.
*
*/
namespace FOodleCompressedArray
{
	/**
	* Compress an arbitrary data pointer, replacing existing data.
	* 
	* @param OutCompressed		The destination TArray.
	* @param InData				The memory to compress.
	* @param InDataSize			The number of bytes in the InData buffer.
	* @param InCompressor		The Oodle compressor to use. See discussion.
	* @param InLevel			The compression level to use. See discussion.
	* @return					Success or failure. Failure is usually bad parameter or out-of-memory.
	*
	* Oodle exposes two knobs for compression - the compression _type_ and the compression _level_. Type
	* more or less trades _decompression_ speed for compression ratio, and level more or less trades
	* _compression_ speed for ratio.
	* 
	* This makes selection very usage case specific, and it's tough to just recommend a one-size-fits-all
	* parameter set. If you have access to Epic Slack, ask in #oodle-dev. Some more detailed explanation
	* exists adjacent to the EOodleDataCompressor and EOodleDataCompressionLevel declarations.
	* 
	* For common use cases, reference GetCompressorAndLevelForCommonUsage()
	* 
	* **NOTE** any data that will be staged to a pak/iostore should NOT be compressed, as that system will
	* compress for you in a manner appropriate for the platform!
	* 
	*/
	bool CORE_API CompressData(TArray<uint8>& OutCompressed, const void* InData, int32 InDataSize, FOodleDataCompression::ECompressor InCompressor, FOodleDataCompression::ECompressionLevel InLevel);
	bool CORE_API CompressData64(TArray64<uint8>& OutCompressed, const void* InData, int64 InDataSize, FOodleDataCompression::ECompressor InCompressor, FOodleDataCompression::ECompressionLevel InLevel);

	/**
	* Provides access to the compressed and decompressed sizes.
	* 
	* @param InCompressed			A TArray generated by a call to one of the FOodleCompressedArray compression functions.
	* @param OutCompressedSize		The amount of compressed data (The array Num() is this plus the header size).
	* @param OutDecompressedSize	The number of bytes decompressing the data will produce.
	* @return						0 if the FCompressedArray isn't valid, or if the compressed array requires PeekSizes64.
	*								Otherwise, the offset to the compressed data in the array.
	*/
	inline int32 PeekSizes(TArray<uint8> const& InCompressed, int32& OutCompressedSize, int32& OutDecompressedSize)
	{
		// At minimum we need 8 bytes.
		if (InCompressed.Num() < sizeof(int32)*2)
		{
			return 0;
		}

		const int32* Sizes = (const int32*)InCompressed.GetData();
		OutDecompressedSize = Sizes[0];
		OutCompressedSize = Sizes[1];

#if !PLATFORM_LITTLE_ENDIAN
		OutDecompressedSize = BYTESWAP_ORDER32(OutDecompressedSize);
		OutCompressedSize = BYTESWAP_ORDER32(OutCompressedSize);
#endif

		// If the compressed size has highest bit set, we are 64 bit,
		// and need to call PeekSizes64.
		if (OutCompressedSize & 0x80000000)
		{
			return 0;
		}

		return sizeof(int32) * 2;
	}
	inline int32 PeekSizes64(TArray64<uint8> const& InCompressed, int64& OutCompressedSize, int64& OutDecompressedSize)
	{
		if (InCompressed.Num() < sizeof(int32)*2)
		{
			return 0;
		}

		const int32* Sizes = (const int32*)InCompressed.GetData();
		int32 DecompressedSize32 = Sizes[0];
		int32 CompressedSize32 = Sizes[1];

#if !PLATFORM_LITTLE_ENDIAN
		DecompressedSize32 = BYTESWAP_ORDER32(DecompressedSize32);
		CompressedSize32 = BYTESWAP_ORDER32(CompressedSize32);
#endif

		OutCompressedSize = CompressedSize32;
		OutDecompressedSize = DecompressedSize32;

		// If the compressed size has highest bit set, we are 64 bit. This
		// is expected to be rare
		if (CompressedSize32 & 0x80000000)
		{
			// Now two int64s.
			if (InCompressed.Num() < sizeof(int64)*2)
			{
				return 0;
			}

			// So now the marker bit is the top bit from decompressed size
			const int64* Sizes64 = (const int64*)InCompressed.GetData();
			int64 DecompressedSize64 = Sizes64[0];
			int64 CompressedSize64 = Sizes64[1];

#if !PLATFORM_LITTLE_ENDIAN
			DecompressedSize64 = BYTESWAP_ORDER64(DecompressedSize64);
			CompressedSize64 = BYTESWAP_ORDER64(CompressedSize64);
#endif

			// clear the 64 bit indicator from decompressed size
			DecompressedSize64 &= ~0x8000000000000000ULL;

			OutDecompressedSize = DecompressedSize64;
			OutCompressedSize = CompressedSize64;

			return sizeof(int64) * 2;
		}

		return sizeof(int32) * 2;
	}

	/**
	* Decompresses a compressed TArray to a buffer that has already been allocated.
	* 
	* @param InDestinationBuffer		The buffer to contain the decompressed data.
	* @param InDestinationBufferSize	Allocated size of InDestinationBuffer; if it is not large enough to hold the decompressed data
	*									(as reported by PeekSizes), this function will return false.
	* @param InCompressed				A TArray created by one of the FOodleCompressedArray compression functions.
	* @return							Success or failure. Failure is usually because the FCompressedArray doesn't
	*									actually contain any data, but might fail because 64 bit is needed.
	*/
	bool CORE_API DecompressToExistingBuffer(void* InDestinationBuffer, int64 InDestinationBufferSize,  TArray<uint8> const& InCompressed);
	bool CORE_API DecompressToExistingBuffer64(void* InDestinationBuffer, int64 InDestinationBufferSize, TArray64<uint8> const& InCompressed);

	/**
	* Decompresses a compressed TArray to a buffer that will be allocated by this function.
	* 
	* @param OutDestinationBuffer		The pointer to contain the allocated buffer that contains the decompressed
	*									data.
	* @param OutDestinationBufferSize	Will recieve the size of the buffer allocated.
	* @param InCompressed				A TArray created by one of the FOodleCompressedArray compression functions.
	* @return							Success or failure. Failure is usually because the TArray doesn't
	*									actually contain any data, but might fail because 64 bit is needed.
	*/
	bool CORE_API DecompressToAllocatedBuffer(void*& OutDestinationBuffer, int32& OutDestinationBufferSize, TArray<uint8> const& InCompressed);
	bool CORE_API DecompressToAllocatedBuffer64(void*& OutDestinationBuffer, int64& OutDestinationBufferSize, TArray64<uint8> const& InCompressed);


	/**
	* Compress an arbitrary data pointer, replacing existing data.
	*
	* This is just a thunk to CompressData, and exists just for type safety
	* so everyone doesn't have to pull out the raw data from their TArrays.
	* 
	* See CompressData for parameter discussion.
	*/
	template <class T>
	bool CompressTArray(TArray<uint8>& OutCompressed, const TArray<T>& InBuffer, FOodleDataCompression::ECompressor InCompressor, FOodleDataCompression::ECompressionLevel InLevel)
	{
		return CompressData(OutCompressed, (const void*)InBuffer.GetData(), InBuffer.Num() * sizeof(T), InCompressor, InLevel);
	}
	template <class T>
	bool CompressTArray64(TArray64<uint8>& OutCompressed, const TArray64<T>& InBuffer, FOodleDataCompression::ECompressor InCompressor, FOodleDataCompression::ECompressionLevel InLevel)
	{
		return CompressData64(OutCompressed, (const void*)InBuffer.GetData(), InBuffer.Num() * sizeof(T), InCompressor, InLevel);
	}

	/**
	* Decompress to a TArray. This should be paired with CompressTArray.
	* 
	* @param OutDecompressed		The output TArray. Existing contents will be destroyed.
	* @param InCompressed			A TArray created by one of the FOodleCompressedArray compression functions.
	* @return						Success or failure. Note that if the compress/decompress TArray
	*								types are mismatched (i.e. have different sizes) this could fail
	*								due to granularity concerns.
	*/
	template <class T>
	bool DecompressToTArray(TArray<T>& OutDecompressed, TArray<uint8> const& InCompressed)
	{
		int32 DecompressedSize, CompressedSize;
		if (PeekSizes(InCompressed, CompressedSize, DecompressedSize) == 0)
		{
			return false;
		}

		if ((DecompressedSize % sizeof(T)) != 0)
		{
			// We must be able to evenly fit our decompressed data in to the desired output.
			return false;
		}

		OutDecompressed.SetNum(DecompressedSize / sizeof(T));
		return DecompressToExistingBuffer(OutDecompressed.GetData(),DecompressedSize, InCompressed);
	}
	template <class T>
	bool DecompressToTArray64(TArray64<T>& OutDecompressed, TArray64<uint8> const& InCompressed)
	{
		int64 DecompressedSize, CompressedSize;
		if (PeekSizes64(InCompressed, CompressedSize, DecompressedSize) == 0)
		{
			return false;
		}

		if ((DecompressedSize % sizeof(T)) != 0)
		{
			// We must be able to evenly fit our decompressed data in to the desired output.
			return false;
		}

		OutDecompressed.SetNum(DecompressedSize / sizeof(T));
		return DecompressToExistingBuffer64(OutDecompressed.GetData(),DecompressedSize, InCompressed);
	}
};

